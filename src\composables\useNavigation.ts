/**
 * Navigation Composable
 * Provides reactive navigation data management and utilities
 */

import { ref, computed, reactive, watch, readonly } from "vue";
import type { NavigationLink, NavigationSection } from "../types/navigation";

export interface NavigationState {
  /** Current active link ID */
  activeLink: string | null;
  /** Loading state for navigation data */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Navigation data */
  navigation: NavigationLink[] | NavigationSection[];
}

export interface NavigationConfig {
  /** Auto-detect active links based on current route */
  autoDetectActive?: boolean;
  /** Base URL for relative links */
  baseUrl?: string;
  /** Default target for external links */
  defaultExternalTarget?: "_blank" | "_self";
  /** Whether to add external link indicators */
  showExternalIndicators?: boolean;
}

export function useNavigation(
  initialNavigation: NavigationLink[] | NavigationSection[] = [],
  config: NavigationConfig = {}
) {
  // Default configuration
  const defaultConfig: Required<NavigationConfig> = {
    autoDetectActive: true,
    baseUrl: "",
    defaultExternalTarget: "_blank",
    showExternalIndicators: true,
  };

  const navigationConfig = reactive({ ...defaultConfig, ...config });

  // Navigation state
  const state = reactive<NavigationState>({
    activeLink: null,
    isLoading: false,
    error: null,
    navigation: initialNavigation,
  });

  // Computed properties
  const flatNavigationLinks = computed((): NavigationLink[] => {
    if (Array.isArray(state.navigation)) {
      if (state.navigation.length > 0 && "links" in state.navigation[0]) {
        // It's NavigationSection[]
        return (state.navigation as NavigationSection[]).flatMap((section) =>
          flattenLinks(section.links)
        );
      }
      // It's NavigationLink[]
      return flattenLinks(state.navigation as NavigationLink[]);
    }
    return [];
  });

  const navigationSections = computed((): NavigationSection[] => {
    if (
      Array.isArray(state.navigation) &&
      state.navigation.length > 0 &&
      "links" in state.navigation[0]
    ) {
      return state.navigation as NavigationSection[];
    }
    return [];
  });

  const hasNestedNavigation = computed((): boolean => {
    return flatNavigationLinks.value.some(
      (link) => link.children && link.children.length > 0
    );
  });

  // Helper functions
  function flattenLinks(links: NavigationLink[]): NavigationLink[] {
    const flattened: NavigationLink[] = [];

    for (const link of links) {
      flattened.push(link);
      if (link.children) {
        flattened.push(...flattenLinks(link.children));
      }
    }

    return flattened;
  }

  function isExternalLink(href: string): boolean {
    try {
      const url = new URL(href, window.location.origin);
      return url.origin !== window.location.origin;
    } catch {
      return false;
    }
  }

  function normalizeLink(link: NavigationLink): NavigationLink {
    const normalized = { ...link };

    // Add base URL if needed
    if (navigationConfig.baseUrl && !isExternalLink(normalized.href)) {
      normalized.href = `${navigationConfig.baseUrl}${normalized.href}`;
    }

    // Set external flag if not explicitly set
    if (normalized.external === undefined) {
      normalized.external = isExternalLink(normalized.href);
    }

    // Normalize children
    if (normalized.children) {
      normalized.children = normalized.children.map(normalizeLink);
    }

    return normalized;
  }

  function normalizeNavigation(
    navigation: NavigationLink[] | NavigationSection[]
  ): NavigationLink[] | NavigationSection[] {
    if (
      Array.isArray(navigation) &&
      navigation.length > 0 &&
      "links" in navigation[0]
    ) {
      // It's NavigationSection[]
      return (navigation as NavigationSection[]).map((section) => ({
        ...section,
        links: section.links.map(normalizeLink),
      }));
    }
    // It's NavigationLink[]
    return (navigation as NavigationLink[]).map(normalizeLink);
  }

  // Navigation management functions
  function setNavigation(navigation: NavigationLink[] | NavigationSection[]) {
    state.isLoading = true;
    state.error = null;

    try {
      state.navigation = normalizeNavigation(navigation);
      state.isLoading = false;
    } catch (error) {
      state.error =
        error instanceof Error ? error.message : "Failed to set navigation";
      state.isLoading = false;
    }
  }

  async function loadNavigation(
    loader: () => Promise<NavigationLink[] | NavigationSection[]>
  ) {
    state.isLoading = true;
    state.error = null;

    try {
      const navigation = await loader();
      state.navigation = normalizeNavigation(navigation);
      state.isLoading = false;
    } catch (error) {
      state.error =
        error instanceof Error ? error.message : "Failed to load navigation";
      state.isLoading = false;
    }
  }

  function addNavigationItem(
    item: NavigationLink,
    parentId?: string,
    position?: number
  ) {
    const navigation = [...(state.navigation as NavigationLink[])];

    if (parentId) {
      // Add to parent's children
      const parent = findLinkById(parentId, navigation);
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }
        if (position !== undefined) {
          parent.children.splice(position, 0, normalizeLink(item));
        } else {
          parent.children.push(normalizeLink(item));
        }
      }
    } else {
      // Add to root level
      if (position !== undefined) {
        navigation.splice(position, 0, normalizeLink(item));
      } else {
        navigation.push(normalizeLink(item));
      }
    }

    state.navigation = navigation;
  }

  function removeNavigationItem(itemId: string) {
    const navigation = [...(state.navigation as NavigationLink[])];

    function removeFromArray(links: NavigationLink[]): NavigationLink[] {
      return links.filter((link) => {
        if (link.id === itemId) {
          return false;
        }
        if (link.children) {
          link.children = removeFromArray(link.children);
        }
        return true;
      });
    }

    state.navigation = removeFromArray(navigation);
  }

  function updateNavigationItem(
    itemId: string,
    updates: Partial<NavigationLink>
  ) {
    const link = findLinkById(itemId);
    if (link) {
      Object.assign(link, updates);
      // Re-normalize if href changed
      if (updates.href) {
        Object.assign(link, normalizeLink(link));
      }
    }
  }

  function findLinkById(
    id: string,
    links?: NavigationLink[]
  ): NavigationLink | null {
    const searchLinks = links || flatNavigationLinks.value;

    for (const link of searchLinks) {
      if (link.id === id) {
        return link;
      }
      if (link.children) {
        const found = findLinkById(id, link.children);
        if (found) {
          return found;
        }
      }
    }

    return null;
  }

  function setActiveLink(linkId: string | null) {
    // Clear previous active state
    flatNavigationLinks.value.forEach((link) => {
      link.active = false;
    });

    // Set new active state
    if (linkId) {
      const link = findLinkById(linkId);
      if (link) {
        link.active = true;
        state.activeLink = linkId;
      }
    } else {
      state.activeLink = null;
    }
  }

  function setActiveLinkByHref(href: string) {
    const link = flatNavigationLinks.value.find((link) => link.href === href);
    if (link) {
      setActiveLink(link.id);
    }
  }

  // Auto-detect active link based on current URL
  function detectActiveLink() {
    if (!navigationConfig.autoDetectActive) return;

    const currentPath = window.location.pathname;
    const currentHref = window.location.href;

    // Find exact match first
    let activeLink = flatNavigationLinks.value.find(
      (link) => link.href === currentPath || link.href === currentHref
    );

    // If no exact match, find the best partial match
    if (!activeLink) {
      const matches = flatNavigationLinks.value
        .filter(
          (link) => currentPath.startsWith(link.href) && link.href !== "/"
        )
        .sort((a, b) => b.href.length - a.href.length); // Longest match first

      activeLink = matches[0];
    }

    if (activeLink) {
      setActiveLink(activeLink.id);
    }
  }

  // Watch for navigation changes to auto-detect active links
  watch(
    () => state.navigation,
    () => {
      if (navigationConfig.autoDetectActive) {
        detectActiveLink();
      }
    },
    { immediate: true, deep: true }
  );

  // Initialize
  if (initialNavigation.length > 0) {
    setNavigation(initialNavigation);
  }

  return {
    // State
    state: readonly(state),
    config: navigationConfig,

    // Computed
    flatNavigationLinks,
    navigationSections,
    hasNestedNavigation,

    // Methods
    setNavigation,
    loadNavigation,
    addNavigationItem,
    removeNavigationItem,
    updateNavigationItem,
    findLinkById,
    setActiveLink,
    setActiveLinkByHref,
    detectActiveLink,
  };
}

// Utility function to create navigation data
export function createNavigationLink(
  id: string,
  label: string,
  href: string,
  options: Partial<Omit<NavigationLink, "id" | "label" | "href">> = {}
): NavigationLink {
  return {
    id,
    label,
    href,
    ...options,
  };
}

export function createNavigationSection(
  id: string,
  links: NavigationLink[],
  title?: string
): NavigationSection {
  return {
    id,
    title,
    links,
  };
}
