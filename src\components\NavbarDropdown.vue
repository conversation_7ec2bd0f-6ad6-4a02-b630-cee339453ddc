<template>
  <div
    v-show="isOpen"
    :class="[
      'navbar__dropdown',
      {
        'navbar__dropdown--open': isOpen,
        'navbar__dropdown--animating': isAnimating,
      }
    ]"
    role="menu"
    :aria-labelledby="`navbar-link-${parentId}`"
  >
    <div class="navbar__dropdown-content">
      <ul class="navbar__dropdown-list" role="none">
        <li
          v-for="link in links"
          :key="link.id"
          class="navbar__dropdown-item"
          role="none"
        >
          <component
            :is="getDropdownLinkComponent(link)"
            :href="link.external ? link.href : undefined"
            :to="!link.external ? link.href : undefined"
            :target="link.external ? '_blank' : undefined"
            :rel="link.external ? 'noopener noreferrer' : undefined"
            :class="[
              'navbar__dropdown-link',
              {
                'navbar__dropdown-link--active': link.active,
                'navbar__dropdown-link--disabled': link.disabled,
              },
              link.className,
            ]"
            :aria-disabled="link.disabled"
            :tabindex="link.disabled ? -1 : 0"
            role="menuitem"
            v-bind="link.attributes"
            @click="handleLinkClick(link, $event)"
            @keydown="handleKeydown(link, $event)"
          >
            <!-- Icon -->
            <span v-if="link.icon" class="navbar__dropdown-link-icon" :aria-hidden="true">
              <component :is="link.icon" v-if="isComponent(link.icon)" />
              <span v-else class="navbar__dropdown-link-icon-text">{{ link.icon }}</span>
            </span>

            <!-- Label -->
            <span class="navbar__dropdown-link-label">{{ link.label }}</span>

            <!-- External Link Icon -->
            <span
              v-if="link.external"
              class="navbar__dropdown-link-external"
              aria-hidden="true"
            >
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 3L3 9M9 3H6M9 3V6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>
          </component>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import type { NavigationLink } from '../types/navigation'

interface Props {
  links: NavigationLink[]
  isOpen: boolean
  parentId: string
}

interface Emits {
  (e: 'link-click', link: NavigationLink, event: Event): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive state
const isAnimating = ref(false)

// Watch for open state changes to handle animations
watch(() => props.isOpen, async (newValue) => {
  if (newValue) {
    isAnimating.value = true
    await nextTick()
    // Focus first link when dropdown opens
    const firstLink = document.querySelector('.navbar__dropdown-link:not([aria-disabled="true"])')
    if (firstLink instanceof HTMLElement) {
      firstLink.focus()
    }
    // Animation duration should match CSS transition
    setTimeout(() => {
      isAnimating.value = false
    }, 250)
  } else {
    isAnimating.value = true
    setTimeout(() => {
      isAnimating.value = false
    }, 250)
  }
})

// Helper functions
const getDropdownLinkComponent = (link: NavigationLink) => {
  if (link.external) {
    return 'a'
  }
  
  if (link.disabled) {
    return 'span'
  }
  
  // For internal links, you might want to use router-link if using Vue Router
  return 'a'
}

const isComponent = (icon: string): boolean => {
  return icon.includes('<') || icon.startsWith('Icon')
}

// Event handlers
const handleLinkClick = (link: NavigationLink, event: Event) => {
  if (link.disabled) {
    event.preventDefault()
    return
  }

  emit('link-click', link, event)
  emit('close')
}

const handleKeydown = (link: NavigationLink, event: KeyboardEvent) => {
  if (link.disabled) {
    return
  }

  const currentElement = event.target as HTMLElement
  const dropdownLinks = Array.from(
    document.querySelectorAll('.navbar__dropdown-link:not([aria-disabled="true"])')
  ) as HTMLElement[]
  
  const currentIndex = dropdownLinks.indexOf(currentElement)

  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      handleLinkClick(link, event)
      break
    
    case 'ArrowDown':
      event.preventDefault()
      const nextIndex = (currentIndex + 1) % dropdownLinks.length
      dropdownLinks[nextIndex]?.focus()
      break
    
    case 'ArrowUp':
      event.preventDefault()
      const prevIndex = currentIndex === 0 ? dropdownLinks.length - 1 : currentIndex - 1
      dropdownLinks[prevIndex]?.focus()
      break
    
    case 'Escape':
      event.preventDefault()
      emit('close')
      // Focus back to the parent trigger
      const parentTrigger = document.querySelector(`[aria-controls="dropdown-${props.parentId}"]`) as HTMLElement
      parentTrigger?.focus()
      break
    
    case 'Tab':
      // Allow natural tab behavior but close dropdown
      emit('close')
      break
  }
}
</script>
