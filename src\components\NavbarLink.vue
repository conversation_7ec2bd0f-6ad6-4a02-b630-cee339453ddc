<template>
  <component
    :is="linkComponent"
    :href="link.external ? link.href : undefined"
    :to="!link.external ? link.href : undefined"
    :target="link.external ? '_blank' : undefined"
    :rel="link.external ? 'noopener noreferrer' : undefined"
    :class="[
      'navbar__link',
      {
        'navbar__link--active': link.active,
        'navbar__link--disabled': link.disabled,
        'navbar__link--has-dropdown': hasDropdown,
        'navbar__link--dropdown-open': isDropdownOpen,
      },
      link.className,
    ]"
    :aria-expanded="hasDropdown ? isDropdownOpen : undefined"
    :aria-haspopup="hasDropdown ? 'menu' : undefined"
    :aria-disabled="link.disabled"
    :tabindex="link.disabled ? -1 : 0"
    role="menuitem"
    v-bind="link.attributes"
    @click="handleClick"
    @keydown="handleKeydown"
  >
    <!-- Icon -->
    <span v-if="link.icon" class="navbar__link-icon" :aria-hidden="true">
      <component :is="link.icon" v-if="isComponent(link.icon)" />
      <span v-else class="navbar__link-icon-text">{{ link.icon }}</span>
    </span>

    <!-- Label -->
    <span class="navbar__link-label">{{ link.label }}</span>

    <!-- Dropdown Arrow -->
    <span
      v-if="hasDropdown"
      class="navbar__link-arrow"
      :class="{ 'navbar__link-arrow--open': isDropdownOpen }"
      aria-hidden="true"
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4 6L8 10L12 6"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </span>

    <!-- External Link Icon -->
    <span
      v-if="link.external"
      class="navbar__link-external"
      aria-hidden="true"
    >
      <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.5 3.5L3.5 10.5M10.5 3.5H7M10.5 3.5V7"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </span>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { NavigationLink } from '../types/navigation'

interface Props {
  link: NavigationLink
  hasDropdown?: boolean
  isDropdownOpen?: boolean
}

interface Emits {
  (e: 'click', link: NavigationLink, event: Event): void
  (e: 'dropdown-toggle', linkId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  hasDropdown: false,
  isDropdownOpen: false,
})

const emit = defineEmits<Emits>()

// Computed properties
const linkComponent = computed(() => {
  // If it's an external link, use 'a' tag
  if (props.link.external) {
    return 'a'
  }
  
  // If it has a dropdown or is disabled, use 'button'
  if (props.hasDropdown || props.link.disabled) {
    return 'button'
  }
  
  // For internal links, you might want to use router-link if using Vue Router
  // For now, we'll use 'a' tag for simplicity
  return 'a'
})

// Helper functions
const isComponent = (icon: string): boolean => {
  // This is a simple check - in a real app, you might have a more sophisticated
  // way to determine if the icon is a component
  return icon.includes('<') || icon.startsWith('Icon')
}

// Event handlers
const handleClick = (event: Event) => {
  if (props.link.disabled) {
    event.preventDefault()
    return
  }

  if (props.hasDropdown) {
    event.preventDefault()
    emit('dropdown-toggle', props.link.id)
  } else {
    emit('click', props.link, event)
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  if (props.link.disabled) {
    return
  }

  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      handleClick(event)
      break
    case 'ArrowDown':
      if (props.hasDropdown) {
        event.preventDefault()
        emit('dropdown-toggle', props.link.id)
      }
      break
    case 'Escape':
      if (props.hasDropdown && props.isDropdownOpen) {
        event.preventDefault()
        emit('dropdown-toggle', props.link.id)
      }
      break
  }
}
</script>
