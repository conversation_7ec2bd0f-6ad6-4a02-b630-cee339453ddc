/**
 * Accessibility Composable
 * Provides enhanced accessibility features for navigation components
 */

import { ref, onMounted, onUnmounted } from 'vue'

export interface AccessibilityConfig {
  /** Enable keyboard navigation */
  enableKeyboardNavigation?: boolean
  /** Enable focus management */
  enableFocusManagement?: boolean
  /** Enable screen reader announcements */
  enableScreenReaderAnnouncements?: boolean
  /** Skip link text */
  skipLinkText?: string
  /** Enable high contrast mode detection */
  enableHighContrastDetection?: boolean
}

export function useAccessibility(config: AccessibilityConfig = {}) {
  const defaultConfig: Required<AccessibilityConfig> = {
    enableKeyboardNavigation: true,
    enableFocusManagement: true,
    enableScreenReaderAnnouncements: true,
    skipLinkText: 'Skip to main content',
    enableHighContrastDetection: true,
  }

  const accessibilityConfig = { ...defaultConfig, ...config }

  // Reactive state
  const isHighContrast = ref(false)
  const isReducedMotion = ref(false)
  const currentFocus = ref<HTMLElement | null>(null)
  const focusHistory = ref<HTMLElement[]>([])

  // Screen reader live region for announcements
  let liveRegion: HTMLElement | null = null

  // Keyboard navigation state
  const keyboardNavigationState = ref({
    isNavigatingWithKeyboard: false,
    lastKeyPressed: '',
    focusableElements: [] as HTMLElement[],
    currentFocusIndex: -1,
  })

  // Initialize accessibility features
  function initializeAccessibility() {
    if (accessibilityConfig.enableScreenReaderAnnouncements) {
      createLiveRegion()
    }

    if (accessibilityConfig.enableHighContrastDetection) {
      detectHighContrast()
      detectReducedMotion()
    }

    if (accessibilityConfig.enableKeyboardNavigation) {
      setupKeyboardNavigation()
    }

    if (accessibilityConfig.enableFocusManagement) {
      setupFocusManagement()
    }
  }

  // Create ARIA live region for screen reader announcements
  function createLiveRegion() {
    liveRegion = document.createElement('div')
    liveRegion.setAttribute('aria-live', 'polite')
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.className = 'sr-only'
    liveRegion.id = 'navbar-live-region'
    document.body.appendChild(liveRegion)
  }

  // Announce message to screen readers
  function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
    if (!accessibilityConfig.enableScreenReaderAnnouncements || !liveRegion) {
      return
    }

    liveRegion.setAttribute('aria-live', priority)
    liveRegion.textContent = message

    // Clear the message after a short delay to allow for re-announcements
    setTimeout(() => {
      if (liveRegion) {
        liveRegion.textContent = ''
      }
    }, 1000)
  }

  // Detect high contrast mode
  function detectHighContrast() {
    const testElement = document.createElement('div')
    testElement.style.border = '1px solid'
    testElement.style.borderColor = 'red green'
    document.body.appendChild(testElement)

    const computedStyle = window.getComputedStyle(testElement)
    const borderTopColor = computedStyle.borderTopColor
    const borderRightColor = computedStyle.borderRightColor

    isHighContrast.value = borderTopColor === borderRightColor

    document.body.removeChild(testElement)
  }

  // Detect reduced motion preference
  function detectReducedMotion() {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    isReducedMotion.value = mediaQuery.matches

    mediaQuery.addEventListener('change', (e) => {
      isReducedMotion.value = e.matches
    })
  }

  // Setup keyboard navigation
  function setupKeyboardNavigation() {
    document.addEventListener('keydown', handleGlobalKeydown)
    document.addEventListener('keyup', handleGlobalKeyup)
  }

  // Setup focus management
  function setupFocusManagement() {
    document.addEventListener('focusin', handleFocusIn)
    document.addEventListener('focusout', handleFocusOut)
  }

  // Global keydown handler
  function handleGlobalKeydown(event: KeyboardEvent) {
    keyboardNavigationState.value.isNavigatingWithKeyboard = true
    keyboardNavigationState.value.lastKeyPressed = event.key

    // Handle skip link
    if (event.key === 'Tab' && !event.shiftKey && event.target === document.body) {
      const skipLink = document.querySelector('[data-skip-link]') as HTMLElement
      if (skipLink) {
        skipLink.focus()
        event.preventDefault()
      }
    }

    // Handle escape key globally
    if (event.key === 'Escape') {
      handleEscapeKey()
    }
  }

  // Global keyup handler
  function handleGlobalKeyup(event: KeyboardEvent) {
    // Reset keyboard navigation state after a delay
    setTimeout(() => {
      if (keyboardNavigationState.value.lastKeyPressed === event.key) {
        keyboardNavigationState.value.isNavigatingWithKeyboard = false
      }
    }, 100)
  }

  // Focus in handler
  function handleFocusIn(event: FocusEvent) {
    const target = event.target as HTMLElement
    if (target && target !== currentFocus.value) {
      // Add to focus history
      if (currentFocus.value) {
        focusHistory.value.push(currentFocus.value)
        // Limit history size
        if (focusHistory.value.length > 10) {
          focusHistory.value.shift()
        }
      }
      currentFocus.value = target
    }
  }

  // Focus out handler
  function handleFocusOut(event: FocusEvent) {
    // Handle focus leaving the component
    const relatedTarget = event.relatedTarget as HTMLElement
    if (!relatedTarget || !event.currentTarget) {
      return
    }

    // Check if focus is leaving a dropdown or mobile menu
    const currentTarget = event.currentTarget as HTMLElement
    if (currentTarget.closest('.navbar__dropdown') && !relatedTarget.closest('.navbar__dropdown')) {
      // Focus left dropdown
      announceToScreenReader('Left dropdown menu')
    }
  }

  // Handle escape key globally
  function handleEscapeKey() {
    // Close any open dropdowns
    const openDropdowns = document.querySelectorAll('.navbar__dropdown--open')
    openDropdowns.forEach(dropdown => {
      const trigger = dropdown.previousElementSibling as HTMLElement
      if (trigger) {
        trigger.click()
        trigger.focus()
      }
    })

    // Close mobile menu
    const mobileMenu = document.querySelector('.navbar__mobile-menu--open')
    if (mobileMenu) {
      const toggle = document.querySelector('.navbar__mobile-toggle') as HTMLElement
      if (toggle) {
        toggle.click()
        toggle.focus()
      }
    }
  }

  // Get focusable elements within a container
  function getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')

    return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[]
  }

  // Focus first focusable element in container
  function focusFirstElement(container: HTMLElement) {
    const focusableElements = getFocusableElements(container)
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
      return true
    }
    return false
  }

  // Focus last focusable element in container
  function focusLastElement(container: HTMLElement) {
    const focusableElements = getFocusableElements(container)
    if (focusableElements.length > 0) {
      focusableElements[focusableElements.length - 1].focus()
      return true
    }
    return false
  }

  // Trap focus within container
  function trapFocus(container: HTMLElement, event: KeyboardEvent) {
    const focusableElements = getFocusableElements(container)
    if (focusableElements.length === 0) return

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    if (event.key === 'Tab') {
      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement.focus()
        }
      }
    }
  }

  // Restore focus to previous element
  function restoreFocus() {
    if (focusHistory.value.length > 0) {
      const previousElement = focusHistory.value.pop()
      if (previousElement && document.contains(previousElement)) {
        previousElement.focus()
        return true
      }
    }
    return false
  }

  // Generate unique ID for accessibility
  function generateId(prefix: string = 'navbar'): string {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
  }

  // Check if element is visible to screen readers
  function isVisibleToScreenReader(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element)
    return !(
      style.display === 'none' ||
      style.visibility === 'hidden' ||
      style.opacity === '0' ||
      element.hasAttribute('aria-hidden') ||
      element.getAttribute('aria-hidden') === 'true'
    )
  }

  // Cleanup function
  function cleanup() {
    if (liveRegion && document.body.contains(liveRegion)) {
      document.body.removeChild(liveRegion)
    }

    document.removeEventListener('keydown', handleGlobalKeydown)
    document.removeEventListener('keyup', handleGlobalKeyup)
    document.removeEventListener('focusin', handleFocusIn)
    document.removeEventListener('focusout', handleFocusOut)
  }

  // Lifecycle
  onMounted(() => {
    initializeAccessibility()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    isHighContrast,
    isReducedMotion,
    currentFocus,
    keyboardNavigationState,

    // Methods
    announceToScreenReader,
    getFocusableElements,
    focusFirstElement,
    focusLastElement,
    trapFocus,
    restoreFocus,
    generateId,
    isVisibleToScreenReader,
    cleanup,
  }
}
