/**
 * Animations Composable
 * Provides enhanced animation utilities and state management
 */

import { ref, computed, nextTick } from 'vue'

export interface AnimationConfig {
  /** Enable animations globally */
  enableAnimations?: boolean
  /** Respect user's reduced motion preference */
  respectReducedMotion?: boolean
  /** Default animation duration in milliseconds */
  defaultDuration?: number
  /** Default animation easing */
  defaultEasing?: string
}

export interface AnimationState {
  /** Whether animations are currently enabled */
  enabled: boolean
  /** Whether user prefers reduced motion */
  reducedMotion: boolean
  /** Currently running animations */
  runningAnimations: Set<string>
}

export function useAnimations(config: AnimationConfig = {}) {
  const defaultConfig: Required<AnimationConfig> = {
    enableAnimations: true,
    respectReducedMotion: true,
    defaultDuration: 250,
    defaultEasing: 'ease-in-out',
  }

  const animationConfig = { ...defaultConfig, ...config }

  // Animation state
  const state = ref<AnimationState>({
    enabled: animationConfig.enableAnimations,
    reducedMotion: false,
    runningAnimations: new Set(),
  })

  // Computed properties
  const shouldAnimate = computed(() => {
    return state.value.enabled && (!state.value.reducedMotion || !animationConfig.respectReducedMotion)
  })

  const animationDuration = computed(() => {
    return shouldAnimate.value ? animationConfig.defaultDuration : 0
  })

  // Initialize reduced motion detection
  function initializeReducedMotion() {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    state.value.reducedMotion = mediaQuery.matches

    mediaQuery.addEventListener('change', (e) => {
      state.value.reducedMotion = e.matches
    })
  }

  // Animation utilities
  function createAnimation(
    element: HTMLElement,
    keyframes: Keyframe[],
    options: KeyframeAnimationOptions = {}
  ): Animation | null {
    if (!shouldAnimate.value) {
      // Apply final state immediately if animations are disabled
      const finalKeyframe = keyframes[keyframes.length - 1]
      Object.assign(element.style, finalKeyframe)
      return null
    }

    const animationOptions: KeyframeAnimationOptions = {
      duration: animationConfig.defaultDuration,
      easing: animationConfig.defaultEasing,
      fill: 'forwards',
      ...options,
    }

    return element.animate(keyframes, animationOptions)
  }

  // Fade in animation
  function fadeIn(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.opacity = '1'
        resolve()
        return
      }

      const animation = createAnimation(
        element,
        [
          { opacity: '0' },
          { opacity: '1' }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Fade out animation
  function fadeOut(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.opacity = '0'
        resolve()
        return
      }

      const animation = createAnimation(
        element,
        [
          { opacity: '1' },
          { opacity: '0' }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Slide down animation
  function slideDown(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.height = 'auto'
        element.style.overflow = 'visible'
        resolve()
        return
      }

      const startHeight = element.scrollHeight
      element.style.height = '0px'
      element.style.overflow = 'hidden'

      const animation = createAnimation(
        element,
        [
          { height: '0px' },
          { height: `${startHeight}px` }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => {
          element.style.height = 'auto'
          element.style.overflow = 'visible'
          resolve()
        })
      } else {
        resolve()
      }
    })
  }

  // Slide up animation
  function slideUp(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.height = '0px'
        element.style.overflow = 'hidden'
        resolve()
        return
      }

      const startHeight = element.scrollHeight
      element.style.height = `${startHeight}px`
      element.style.overflow = 'hidden'

      const animation = createAnimation(
        element,
        [
          { height: `${startHeight}px` },
          { height: '0px' }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Scale in animation
  function scaleIn(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.transform = 'scale(1)'
        element.style.opacity = '1'
        resolve()
        return
      }

      const animation = createAnimation(
        element,
        [
          { transform: 'scale(0.95)', opacity: '0' },
          { transform: 'scale(1)', opacity: '1' }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Scale out animation
  function scaleOut(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.transform = 'scale(0.95)'
        element.style.opacity = '0'
        resolve()
        return
      }

      const animation = createAnimation(
        element,
        [
          { transform: 'scale(1)', opacity: '1' },
          { transform: 'scale(0.95)', opacity: '0' }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Dropdown animation (combination of scale and fade)
  function animateDropdownOpen(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.opacity = '1'
        element.style.visibility = 'visible'
        element.style.transform = 'translateY(0)'
        resolve()
        return
      }

      element.style.visibility = 'visible'

      const animation = createAnimation(
        element,
        [
          { 
            opacity: '0', 
            transform: 'translateY(-0.5rem) scale(0.95)' 
          },
          { 
            opacity: '1', 
            transform: 'translateY(0) scale(1)' 
          }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Dropdown close animation
  function animateDropdownClose(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.opacity = '0'
        element.style.visibility = 'hidden'
        element.style.transform = 'translateY(-0.5rem)'
        resolve()
        return
      }

      const animation = createAnimation(
        element,
        [
          { 
            opacity: '1', 
            transform: 'translateY(0) scale(1)' 
          },
          { 
            opacity: '0', 
            transform: 'translateY(-0.5rem) scale(0.95)' 
          }
        ],
        { duration }
      )

      if (animation) {
        animation.addEventListener('finish', () => {
          element.style.visibility = 'hidden'
          resolve()
        })
      } else {
        resolve()
      }
    })
  }

  // Mobile menu slide animation
  function animateMobileMenuOpen(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.transform = 'translateY(0)'
        resolve()
        return
      }

      const animation = createAnimation(
        element,
        [
          { transform: 'translateY(-100%)' },
          { transform: 'translateY(0)' }
        ],
        { duration, easing: 'cubic-bezier(0.4, 0, 0.2, 1)' }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Mobile menu close animation
  function animateMobileMenuClose(
    element: HTMLElement,
    duration: number = animationConfig.defaultDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!shouldAnimate.value) {
        element.style.transform = 'translateY(-100%)'
        resolve()
        return
      }

      const animation = createAnimation(
        element,
        [
          { transform: 'translateY(0)' },
          { transform: 'translateY(-100%)' }
        ],
        { duration, easing: 'cubic-bezier(0.4, 0, 0.2, 1)' }
      )

      if (animation) {
        animation.addEventListener('finish', () => resolve())
      } else {
        resolve()
      }
    })
  }

  // Stagger animation for multiple elements
  function staggerAnimation(
    elements: HTMLElement[],
    animationFn: (element: HTMLElement) => Promise<void>,
    staggerDelay: number = 50
  ): Promise<void[]> {
    return Promise.all(
      elements.map((element, index) => {
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            animationFn(element).then(resolve)
          }, index * staggerDelay)
        })
      })
    )
  }

  // Initialize
  initializeReducedMotion()

  return {
    // State
    state,
    shouldAnimate,
    animationDuration,

    // Basic animations
    fadeIn,
    fadeOut,
    slideDown,
    slideUp,
    scaleIn,
    scaleOut,

    // Component-specific animations
    animateDropdownOpen,
    animateDropdownClose,
    animateMobileMenuOpen,
    animateMobileMenuClose,

    // Utilities
    createAnimation,
    staggerAnimation,
  }
}
