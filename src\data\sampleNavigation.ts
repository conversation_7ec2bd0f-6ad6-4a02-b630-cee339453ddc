/**
 * Sample Navigation Data
 * Demonstrates various navigation configurations and structures
 */

import type { NavigationLink, NavigationSection } from '../types/navigation'

// Simple navigation links array
export const simpleNavigation: NavigationLink[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    active: true,
  },
  {
    id: 'about',
    label: 'About',
    href: '/about',
  },
  {
    id: 'services',
    label: 'Services',
    href: '/services',
  },
  {
    id: 'contact',
    label: 'Contact',
    href: '/contact',
  },
]

// Navigation with dropdowns
export const navigationWithDropdowns: NavigationLink[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    active: true,
  },
  {
    id: 'products',
    label: 'Products',
    href: '/products',
    children: [
      {
        id: 'web-apps',
        label: 'Web Applications',
        href: '/products/web-apps',
      },
      {
        id: 'mobile-apps',
        label: 'Mobile Applications',
        href: '/products/mobile-apps',
      },
      {
        id: 'desktop-apps',
        label: 'Desktop Applications',
        href: '/products/desktop-apps',
      },
      {
        id: 'apis',
        label: 'APIs & Integrations',
        href: '/products/apis',
      },
    ],
  },
  {
    id: 'solutions',
    label: 'Solutions',
    href: '/solutions',
    children: [
      {
        id: 'enterprise',
        label: 'Enterprise Solutions',
        href: '/solutions/enterprise',
      },
      {
        id: 'startups',
        label: 'Startup Solutions',
        href: '/solutions/startups',
      },
      {
        id: 'consulting',
        label: 'Consulting Services',
        href: '/solutions/consulting',
      },
    ],
  },
  {
    id: 'resources',
    label: 'Resources',
    href: '/resources',
    children: [
      {
        id: 'documentation',
        label: 'Documentation',
        href: '/resources/docs',
      },
      {
        id: 'tutorials',
        label: 'Tutorials',
        href: '/resources/tutorials',
      },
      {
        id: 'blog',
        label: 'Blog',
        href: '/resources/blog',
      },
      {
        id: 'support',
        label: 'Support Center',
        href: '/resources/support',
      },
      {
        id: 'community',
        label: 'Community Forum',
        href: 'https://community.example.com',
        external: true,
      },
    ],
  },
  {
    id: 'pricing',
    label: 'Pricing',
    href: '/pricing',
  },
  {
    id: 'contact',
    label: 'Contact',
    href: '/contact',
  },
]

// Navigation with icons (using text icons for demo)
export const navigationWithIcons: NavigationLink[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/dashboard',
    icon: '📊',
    active: true,
  },
  {
    id: 'projects',
    label: 'Projects',
    href: '/projects',
    icon: '📁',
    children: [
      {
        id: 'active-projects',
        label: 'Active Projects',
        href: '/projects/active',
        icon: '🟢',
      },
      {
        id: 'completed-projects',
        label: 'Completed Projects',
        href: '/projects/completed',
        icon: '✅',
      },
      {
        id: 'archived-projects',
        label: 'Archived Projects',
        href: '/projects/archived',
        icon: '📦',
      },
    ],
  },
  {
    id: 'team',
    label: 'Team',
    href: '/team',
    icon: '👥',
  },
  {
    id: 'analytics',
    label: 'Analytics',
    href: '/analytics',
    icon: '📈',
  },
  {
    id: 'settings',
    label: 'Settings',
    href: '/settings',
    icon: '⚙️',
    children: [
      {
        id: 'profile',
        label: 'Profile Settings',
        href: '/settings/profile',
        icon: '👤',
      },
      {
        id: 'notifications',
        label: 'Notifications',
        href: '/settings/notifications',
        icon: '🔔',
      },
      {
        id: 'security',
        label: 'Security',
        href: '/settings/security',
        icon: '🔒',
      },
      {
        id: 'billing',
        label: 'Billing',
        href: '/settings/billing',
        icon: '💳',
      },
    ],
  },
]

// Navigation sections (grouped navigation)
export const navigationSections: NavigationSection[] = [
  {
    id: 'main',
    title: 'Main Navigation',
    links: [
      {
        id: 'home',
        label: 'Home',
        href: '/',
        active: true,
      },
      {
        id: 'about',
        label: 'About Us',
        href: '/about',
      },
      {
        id: 'services',
        label: 'Our Services',
        href: '/services',
      },
    ],
  },
  {
    id: 'products',
    title: 'Products',
    links: [
      {
        id: 'software',
        label: 'Software Solutions',
        href: '/products/software',
      },
      {
        id: 'hardware',
        label: 'Hardware Products',
        href: '/products/hardware',
      },
      {
        id: 'consulting',
        label: 'Consulting Services',
        href: '/products/consulting',
      },
    ],
  },
  {
    id: 'support',
    title: 'Support & Resources',
    links: [
      {
        id: 'documentation',
        label: 'Documentation',
        href: '/docs',
      },
      {
        id: 'support-center',
        label: 'Support Center',
        href: '/support',
      },
      {
        id: 'community',
        label: 'Community',
        href: 'https://community.example.com',
        external: true,
      },
    ],
  },
]

// E-commerce style navigation
export const ecommerceNavigation: NavigationLink[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    active: true,
  },
  {
    id: 'shop',
    label: 'Shop',
    href: '/shop',
    children: [
      {
        id: 'electronics',
        label: 'Electronics',
        href: '/shop/electronics',
        children: [
          {
            id: 'smartphones',
            label: 'Smartphones',
            href: '/shop/electronics/smartphones',
          },
          {
            id: 'laptops',
            label: 'Laptops',
            href: '/shop/electronics/laptops',
          },
          {
            id: 'accessories',
            label: 'Accessories',
            href: '/shop/electronics/accessories',
          },
        ],
      },
      {
        id: 'clothing',
        label: 'Clothing',
        href: '/shop/clothing',
        children: [
          {
            id: 'mens',
            label: "Men's Clothing",
            href: '/shop/clothing/mens',
          },
          {
            id: 'womens',
            label: "Women's Clothing",
            href: '/shop/clothing/womens',
          },
          {
            id: 'kids',
            label: "Kids' Clothing",
            href: '/shop/clothing/kids',
          },
        ],
      },
      {
        id: 'home-garden',
        label: 'Home & Garden',
        href: '/shop/home-garden',
      },
    ],
  },
  {
    id: 'deals',
    label: 'Deals',
    href: '/deals',
  },
  {
    id: 'brands',
    label: 'Brands',
    href: '/brands',
  },
  {
    id: 'customer-service',
    label: 'Customer Service',
    href: '/customer-service',
    children: [
      {
        id: 'contact-us',
        label: 'Contact Us',
        href: '/customer-service/contact',
      },
      {
        id: 'shipping-info',
        label: 'Shipping Information',
        href: '/customer-service/shipping',
      },
      {
        id: 'returns',
        label: 'Returns & Exchanges',
        href: '/customer-service/returns',
      },
      {
        id: 'faq',
        label: 'FAQ',
        href: '/customer-service/faq',
      },
    ],
  },
]

// Brand configuration examples
export const brandConfigs = {
  simple: {
    name: 'Your Brand',
    href: '/',
  },
  withLogo: {
    name: 'Your Brand',
    logo: '/logo.svg',
    href: '/',
    alt: 'Your Brand Logo',
  },
  logoOnly: {
    logo: '/logo.svg',
    href: '/',
    alt: 'Your Brand',
  },
}

// Export default navigation for demo
export const defaultNavigation = navigationWithDropdowns
export const defaultBrand = brandConfigs.simple
