<template>
  <div class="navbar__mobile-link-wrapper">
    <!-- Main Link -->
    <component
      :is="linkComponent"
      :href="link.external ? link.href : undefined"
      :to="!link.external ? link.href : undefined"
      :target="link.external ? '_blank' : undefined"
      :rel="link.external ? 'noopener noreferrer' : undefined"
      :class="[
        'navbar__mobile-link',
        {
          'navbar__mobile-link--active': link.active,
          'navbar__mobile-link--disabled': link.disabled,
          'navbar__mobile-link--has-children': hasChildren,
          'navbar__mobile-link--expanded': isExpanded,
        },
        link.className,
      ]"
      :aria-expanded="hasChildren ? isExpanded : undefined"
      :aria-disabled="link.disabled"
      :tabindex="link.disabled ? -1 : 0"
      role="menuitem"
      v-bind="link.attributes"
      @click="handleClick"
    >
      <!-- Icon -->
      <span v-if="link.icon" class="navbar__mobile-link-icon" :aria-hidden="true">
        <component :is="link.icon" v-if="isComponent(link.icon)" />
        <span v-else class="navbar__mobile-link-icon-text">{{ link.icon }}</span>
      </span>

      <!-- Label -->
      <span class="navbar__mobile-link-label">{{ link.label }}</span>

      <!-- Expand/Collapse Arrow for children -->
      <span
        v-if="hasChildren"
        class="navbar__mobile-link-arrow"
        :class="{ 'navbar__mobile-link-arrow--expanded': isExpanded }"
        aria-hidden="true"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6 4L10 8L6 12"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </span>

      <!-- External Link Icon -->
      <span
        v-if="link.external"
        class="navbar__mobile-link-external"
        aria-hidden="true"
      >
        <svg
          width="14"
          height="14"
          viewBox="0 0 14 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.5 3.5L3.5 10.5M10.5 3.5H7M10.5 3.5V7"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </span>
    </component>

    <!-- Children Links (Collapsible) -->
    <div
      v-if="hasChildren"
      class="navbar__mobile-submenu"
      :class="{ 'navbar__mobile-submenu--expanded': isExpanded }"
    >
      <ul class="navbar__mobile-submenu-list" role="menu">
        <li
          v-for="child in link.children"
          :key="child.id"
          class="navbar__mobile-submenu-item"
          role="none"
        >
          <component
            :is="getChildLinkComponent(child)"
            :href="child.external ? child.href : undefined"
            :to="!child.external ? child.href : undefined"
            :target="child.external ? '_blank' : undefined"
            :rel="child.external ? 'noopener noreferrer' : undefined"
            :class="[
              'navbar__mobile-submenu-link',
              {
                'navbar__mobile-submenu-link--active': child.active,
                'navbar__mobile-submenu-link--disabled': child.disabled,
              },
              child.className,
            ]"
            :aria-disabled="child.disabled"
            :tabindex="child.disabled ? -1 : 0"
            role="menuitem"
            v-bind="child.attributes"
            @click="handleChildClick(child, $event)"
          >
            <!-- Child Icon -->
            <span v-if="child.icon" class="navbar__mobile-submenu-link-icon" :aria-hidden="true">
              <component :is="child.icon" v-if="isComponent(child.icon)" />
              <span v-else class="navbar__mobile-submenu-link-icon-text">{{ child.icon }}</span>
            </span>

            <!-- Child Label -->
            <span class="navbar__mobile-submenu-link-label">{{ child.label }}</span>

            <!-- Child External Link Icon -->
            <span
              v-if="child.external"
              class="navbar__mobile-submenu-link-external"
              aria-hidden="true"
            >
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 3L3 9M9 3H6M9 3V6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>
          </component>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { NavigationLink } from '../types/navigation'

interface Props {
  link: NavigationLink
}

interface Emits {
  (e: 'click', link: NavigationLink, event: Event): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive state
const isExpanded = ref(false)

// Computed properties
const hasChildren = computed(() => {
  return Boolean(props.link.children && props.link.children.length > 0)
})

const linkComponent = computed(() => {
  if (props.link.external) {
    return 'a'
  }
  
  if (hasChildren.value || props.link.disabled) {
    return 'button'
  }
  
  return 'a'
})

// Helper functions
const getChildLinkComponent = (child: NavigationLink) => {
  if (child.external) {
    return 'a'
  }
  
  if (child.disabled) {
    return 'span'
  }
  
  return 'a'
}

const isComponent = (icon: string): boolean => {
  return icon.includes('<') || icon.startsWith('Icon')
}

// Event handlers
const handleClick = (event: Event) => {
  if (props.link.disabled) {
    event.preventDefault()
    return
  }

  if (hasChildren.value) {
    event.preventDefault()
    isExpanded.value = !isExpanded.value
  } else {
    emit('click', props.link, event)
  }
}

const handleChildClick = (child: NavigationLink, event: Event) => {
  if (child.disabled) {
    event.preventDefault()
    return
  }

  emit('click', child, event)
}
</script>
