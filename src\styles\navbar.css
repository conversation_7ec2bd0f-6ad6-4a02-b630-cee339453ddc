/**
 * Navbar Component Styles
 * Mobile-first responsive design with comprehensive styling
 */

/* ==========================================================================
   Base Navbar Styles
   ========================================================================== */

.navbar {
  position: relative;
  background-color: var(--navbar-background);
  border-bottom: 1px solid var(--navbar-border);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-sticky);
}

.navbar--sticky {
  position: sticky;
  top: 0;
}

.navbar__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-4);
  max-width: 100%;
}

/* Container size variants */
.container--sm { max-width: var(--breakpoint-sm); }
.container--md { max-width: var(--breakpoint-md); }
.container--lg { max-width: var(--breakpoint-lg); }
.container--xl { max-width: var(--breakpoint-xl); }
.container--2xl { max-width: var(--breakpoint-2xl); }

/* ==========================================================================
   Brand/Logo Styles
   ========================================================================== */

.navbar__brand {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.navbar__brand-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--navbar-text);
  transition: opacity var(--transition-fast);
}

.navbar__brand-link:hover {
  opacity: 0.8;
}

.navbar__brand-logo {
  height: 2rem;
  width: auto;
  object-fit: contain;
}

.navbar__brand-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--navbar-text);
}

/* ==========================================================================
   Desktop Navigation Styles
   ========================================================================== */

.navbar__nav {
  display: none;
}

.navbar__nav--desktop {
  display: none;
}

.navbar__nav-list {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0;
  padding: 0;
  list-style: none;
}

.navbar__nav-item {
  position: relative;
}

/* ==========================================================================
   Navigation Link Styles
   ========================================================================== */

.navbar__link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--navbar-text);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  background: none;
}

.navbar__link:hover {
  color: var(--navbar-text-hover);
  background-color: var(--color-surface-secondary);
}

.navbar__link:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.navbar__link--active {
  color: var(--navbar-text-active);
  background-color: var(--color-primary-50);
}

.navbar__link--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.navbar__link--has-dropdown {
  position: relative;
}

.navbar__link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
}

.navbar__link-label {
  white-space: nowrap;
}

.navbar__link-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-fast);
}

.navbar__link-arrow--open {
  transform: rotate(180deg);
}

.navbar__link-external {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

/* ==========================================================================
   Dropdown Styles
   ========================================================================== */

.navbar__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 12rem;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-0.5rem);
  transition: all var(--transition-base);
}

.navbar__dropdown--open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.navbar__dropdown-content {
  padding: var(--space-2);
}

.navbar__dropdown-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.navbar__dropdown-item {
  margin: 0;
}

.navbar__dropdown-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  text-decoration: none;
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  background: none;
}

.navbar__dropdown-link:hover {
  background-color: var(--color-surface-secondary);
  color: var(--navbar-text-hover);
}

.navbar__dropdown-link:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 1px;
}

.navbar__dropdown-link--active {
  background-color: var(--color-primary-50);
  color: var(--navbar-text-active);
}

.navbar__dropdown-link--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.navbar__dropdown-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
}

.navbar__dropdown-link-external {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  margin-left: auto;
}

/* ==========================================================================
   Mobile Menu Toggle Styles
   ========================================================================== */

.navbar__mobile-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  background: none;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.navbar__mobile-toggle:hover {
  background-color: var(--color-surface-secondary);
}

.navbar__mobile-toggle:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.navbar__mobile-toggle-icon {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
}

.navbar__mobile-toggle-line {
  position: absolute;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--navbar-text);
  border-radius: 1px;
  transition: all var(--transition-base);
  transform-origin: center;
}

.navbar__mobile-toggle-line:nth-child(1) {
  top: 0.25rem;
}

.navbar__mobile-toggle-line:nth-child(2) {
  top: 0.6875rem;
}

.navbar__mobile-toggle-line:nth-child(3) {
  top: 1.125rem;
}

/* Mobile toggle animation when menu is open */
.navbar--mobile-open .navbar__mobile-toggle-line:nth-child(1) {
  top: 0.6875rem;
  transform: rotate(45deg);
}

.navbar--mobile-open .navbar__mobile-toggle-line:nth-child(2) {
  opacity: 0;
}

.navbar--mobile-open .navbar__mobile-toggle-line:nth-child(3) {
  top: 0.6875rem;
  transform: rotate(-45deg);
}

/* ==========================================================================
   Mobile Menu Styles
   ========================================================================== */

.navbar__mobile-menu {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--navbar-background);
  border-top: 1px solid var(--navbar-border);
  max-height: calc(100vh - 100%);
  overflow-y: auto;
  transform: translateY(-100%);
  transition: transform var(--transition-base);
  z-index: var(--z-fixed);
}

.navbar__mobile-menu--open {
  transform: translateY(0);
}

.navbar__mobile-menu-content {
  padding: var(--space-4);
}

.navbar__mobile-nav-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.navbar__mobile-nav-item {
  margin-bottom: var(--space-1);
}

.navbar__mobile-nav-item:last-child {
  margin-bottom: 0;
}

/* ==========================================================================
   Mobile Link Styles
   ========================================================================== */

.navbar__mobile-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--navbar-text);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  background: none;
  text-align: left;
}

.navbar__mobile-link:hover {
  background-color: var(--color-surface-secondary);
  color: var(--navbar-text-hover);
}

.navbar__mobile-link:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.navbar__mobile-link--active {
  background-color: var(--color-primary-50);
  color: var(--navbar-text-active);
}

.navbar__mobile-link--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.navbar__mobile-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  margin-right: var(--space-3);
}

.navbar__mobile-link-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-fast);
}

.navbar__mobile-link-arrow--expanded {
  transform: rotate(90deg);
}

.navbar__mobile-link-external {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

/* ==========================================================================
   Mobile Submenu Styles
   ========================================================================== */

.navbar__mobile-submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-base);
}

.navbar__mobile-submenu--expanded {
  max-height: 20rem; /* Adjust based on content */
}

.navbar__mobile-submenu-list {
  margin: 0;
  padding: 0;
  list-style: none;
  padding-left: var(--space-6);
  padding-top: var(--space-2);
}

.navbar__mobile-submenu-item {
  margin-bottom: var(--space-1);
}

.navbar__mobile-submenu-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  background: none;
  text-align: left;
}

.navbar__mobile-submenu-link:hover {
  background-color: var(--color-surface-secondary);
  color: var(--navbar-text-hover);
}

.navbar__mobile-submenu-link--active {
  background-color: var(--color-primary-50);
  color: var(--navbar-text-active);
}

.navbar__mobile-submenu-link--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.navbar__mobile-submenu-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
}

.navbar__mobile-submenu-link-external {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  margin-left: auto;
}

/* ==========================================================================
   Mobile Overlay
   ========================================================================== */

.navbar__mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--navbar-mobile-overlay);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  animation: fadeIn var(--transition-base) forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* ==========================================================================
   Responsive Design - Tablet and Desktop
   ========================================================================== */

@media (min-width: 768px) {
  .navbar__container {
    padding: var(--space-4) var(--space-6);
  }

  .navbar__brand-logo {
    height: 2.25rem;
  }

  .navbar__brand-name {
    font-size: var(--font-size-xl);
  }

  /* Show desktop navigation, hide mobile toggle */
  .navbar__nav--desktop {
    display: flex;
  }

  .navbar__mobile-toggle {
    display: none;
  }

  /* Hide mobile menu on larger screens */
  .navbar__mobile-menu {
    display: none;
  }

  .navbar__mobile-overlay {
    display: none;
  }
}

@media (min-width: 1024px) {
  .navbar__container {
    padding: var(--space-4) var(--space-8);
  }

  .navbar__nav-list {
    gap: var(--space-4);
  }

  .navbar__link {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
  }
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .navbar__link,
  .navbar__dropdown,
  .navbar__mobile-toggle-line,
  .navbar__mobile-menu,
  .navbar__mobile-submenu {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .navbar {
    border-bottom-width: 2px;
  }

  .navbar__link:focus-visible,
  .navbar__dropdown-link:focus-visible,
  .navbar__mobile-toggle:focus-visible {
    outline-width: 3px;
  }
}

/* Print styles */
@media print {
  .navbar__mobile-toggle,
  .navbar__dropdown,
  .navbar__mobile-menu,
  .navbar__mobile-overlay {
    display: none !important;
  }

  .navbar {
    position: static;
    box-shadow: none;
    border-bottom: 1px solid #000;
  }
}
