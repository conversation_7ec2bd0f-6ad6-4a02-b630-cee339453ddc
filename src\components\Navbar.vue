<template>
  <nav
    :class="[
      'navbar',
      {
        'navbar--sticky': sticky,
        'navbar--mobile-open': mobileMenu.isOpen,
      },
      className,
    ]"
    role="navigation"
    :aria-label="ariaLabel"
  >
    <div :class="containerClass">
      <!-- Brand/Logo Section -->
      <div class="navbar__brand">
        <component
          :is="brand?.href ? 'a' : 'div'"
          v-if="brand"
          :href="brand.href"
          class="navbar__brand-link"
          @click="handleBrandClick"
        >
          <img
            v-if="brand.logo"
            :src="brand.logo"
            :alt="brand.alt || brand.name || 'Logo'"
            class="navbar__brand-logo"
          />
          <span v-if="brand.name" class="navbar__brand-name">
            {{ brand.name }}
          </span>
        </component>
      </div>

      <!-- Desktop Navigation -->
      <div class="navbar__nav navbar__nav--desktop">
        <ul class="navbar__nav-list" role="menubar">
          <li
            v-for="item in navigationItems"
            :key="item.id"
            class="navbar__nav-item"
            role="none"
          >
            <NavbarLink
              :link="item"
              :has-dropdown="hasChildren(item)"
              :is-dropdown-open="dropdown.openDropdown === item.id"
              @click="handleLinkClick"
              @dropdown-toggle="handleDropdownToggle"
            />
            
            <!-- Dropdown Menu -->
            <NavbarDropdown
              v-if="hasChildren(item)"
              :links="item.children!"
              :is-open="dropdown.openDropdown === item.id"
              :parent-id="item.id"
              @link-click="handleLinkClick"
              @close="closeDropdown"
            />
          </li>
        </ul>
      </div>

      <!-- Mobile Menu Toggle -->
      <button
        v-if="showMobileToggle"
        class="navbar__mobile-toggle"
        type="button"
        :aria-expanded="mobileMenu.isOpen"
        aria-controls="mobile-menu"
        aria-label="Toggle mobile menu"
        @click="toggleMobileMenu"
      >
        <span class="navbar__mobile-toggle-icon">
          <span class="navbar__mobile-toggle-line"></span>
          <span class="navbar__mobile-toggle-line"></span>
          <span class="navbar__mobile-toggle-line"></span>
        </span>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div
      v-if="showMobileToggle"
      id="mobile-menu"
      class="navbar__mobile-menu"
      :class="{ 'navbar__mobile-menu--open': mobileMenu.isOpen }"
      :aria-hidden="!mobileMenu.isOpen"
    >
      <div class="navbar__mobile-menu-content">
        <ul class="navbar__mobile-nav-list" role="menu">
          <li
            v-for="item in navigationItems"
            :key="`mobile-${item.id}`"
            class="navbar__mobile-nav-item"
            role="none"
          >
            <NavbarMobileLink
              :link="item"
              @click="handleMobileLinkClick"
            />
          </li>
        </ul>
      </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div
      v-if="showMobileToggle && mobileMenu.isOpen"
      class="navbar__mobile-overlay"
      @click="closeMobileMenu"
      aria-hidden="true"
    ></div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { 
  NavigationLink, 
  NavigationSection, 
  NavbarProps, 
  NavbarEmits,
  MobileMenuState,
  DropdownState 
} from '../types/navigation'
import NavbarLink from './NavbarLink.vue'
import NavbarDropdown from './NavbarDropdown.vue'
import NavbarMobileLink from './NavbarMobileLink.vue'

// Props
const props = withDefaults(defineProps<NavbarProps>(), {
  sticky: false,
  showMobileToggle: true,
  maxWidth: 'xl',
  className: '',
})

// Emits
const emit = defineEmits<NavbarEmits>()

// Reactive state
const mobileMenu = ref<MobileMenuState>({
  isOpen: false,
  isAnimating: false,
})

const dropdown = ref<DropdownState>({
  openDropdown: null,
  isAnimating: false,
})

// Computed properties
const navigationItems = computed((): NavigationLink[] => {
  if (Array.isArray(props.navigation)) {
    // Check if it's an array of NavigationSection or NavigationLink
    if (props.navigation.length > 0 && 'links' in props.navigation[0]) {
      // It's NavigationSection[]
      return (props.navigation as NavigationSection[]).flatMap(section => section.links)
    }
    // It's NavigationLink[]
    return props.navigation as NavigationLink[]
  }
  return []
})

const containerClass = computed(() => {
  const baseClass = 'navbar__container'
  const maxWidthClass = props.maxWidth !== 'full' ? `container--${props.maxWidth}` : 'container'
  return `${baseClass} ${maxWidthClass}`
})

const ariaLabel = computed(() => {
  return props.brand?.name ? `${props.brand.name} navigation` : 'Main navigation'
})

// Helper functions
const hasChildren = (item: NavigationLink): boolean => {
  return Boolean(item.children && item.children.length > 0)
}

// Event handlers
const handleBrandClick = (event: Event) => {
  emit('brand-click', event)
}

const handleLinkClick = (link: NavigationLink, event: Event) => {
  // Close mobile menu if open
  if (mobileMenu.value.isOpen) {
    closeMobileMenu()
  }
  
  // Close any open dropdown
  closeDropdown()
  
  emit('link-click', link, event)
}

const handleMobileLinkClick = (link: NavigationLink, event: Event) => {
  closeMobileMenu()
  emit('link-click', link, event)
}

const handleDropdownToggle = (linkId: string) => {
  if (dropdown.value.openDropdown === linkId) {
    closeDropdown()
  } else {
    dropdown.value.openDropdown = linkId
  }
}

const toggleMobileMenu = () => {
  if (mobileMenu.value.isOpen) {
    closeMobileMenu()
  } else {
    openMobileMenu()
  }
}

const openMobileMenu = () => {
  mobileMenu.value.isOpen = true
  emit('mobile-toggle', true)
  
  // Prevent body scroll when mobile menu is open
  document.body.style.overflow = 'hidden'
}

const closeMobileMenu = () => {
  mobileMenu.value.isOpen = false
  emit('mobile-toggle', false)
  
  // Restore body scroll
  document.body.style.overflow = ''
}

const closeDropdown = () => {
  dropdown.value.openDropdown = null
}

// Click outside handler for dropdowns
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.navbar__nav-item')) {
    closeDropdown()
  }
}

// Escape key handler
const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    if (mobileMenu.value.isOpen) {
      closeMobileMenu()
    }
    if (dropdown.value.openDropdown) {
      closeDropdown()
    }
  }
}

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleEscapeKey)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleEscapeKey)
  
  // Ensure body scroll is restored
  document.body.style.overflow = ''
})
</script>
