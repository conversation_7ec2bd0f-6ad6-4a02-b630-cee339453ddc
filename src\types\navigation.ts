/**
 * Navigation Types and Interfaces
 * Defines the structure for navigation links and related data
 */

export interface NavigationLink {
  /** Unique identifier for the navigation link */
  id: string;
  /** Display text for the navigation link */
  label: string;
  /** URL or route path for the navigation link */
  href: string;
  /** Optional icon name or component */
  icon?: string;
  /** Whether the link opens in a new tab */
  external?: boolean;
  /** Whether the link is currently active */
  active?: boolean;
  /** Whether the link is disabled */
  disabled?: boolean;
  /** Child navigation links for dropdown menus */
  children?: NavigationLink[];
  /** Additional CSS classes */
  className?: string;
  /** Custom attributes for the link element */
  attributes?: Record<string, string>;
}

export interface NavigationSection {
  /** Unique identifier for the section */
  id: string;
  /** Display title for the section (optional) */
  title?: string;
  /** Navigation links in this section */
  links: NavigationLink[];
}

export interface NavbarProps {
  /** Array of navigation links or sections */
  navigation: NavigationLink[] | NavigationSection[];
  /** Brand/logo configuration */
  brand?: {
    /** Brand name or text */
    name?: string;
    /** Brand logo URL */
    logo?: string;
    /** Brand link URL */
    href?: string;
    /** Alt text for logo */
    alt?: string;
  };
  /** Whether the navbar should be sticky */
  sticky?: boolean;
  /** Custom CSS classes for the navbar */
  className?: string;
  /** Whether to show the mobile menu toggle */
  showMobileToggle?: boolean;
  /** Maximum width for the navbar content */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

export interface NavbarEmits {
  /** Emitted when a navigation link is clicked */
  'link-click': [link: NavigationLink, event: Event];
  /** Emitted when the mobile menu is toggled */
  'mobile-toggle': [isOpen: boolean];
  /** Emitted when the brand/logo is clicked */
  'brand-click': [event: Event];
}

export interface MobileMenuState {
  /** Whether the mobile menu is open */
  isOpen: boolean;
  /** Whether the mobile menu is animating */
  isAnimating: boolean;
}

export interface DropdownState {
  /** ID of the currently open dropdown */
  openDropdown: string | null;
  /** Whether any dropdown is animating */
  isAnimating: boolean;
}
