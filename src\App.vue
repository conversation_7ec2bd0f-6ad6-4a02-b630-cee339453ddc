<script setup lang="ts">
import Navbar from "./components/Navbar.vue";
import { defaultNavigation, defaultBrand } from "./data/sampleNavigation";
import type { NavigationLink } from "./types/navigation";

// Event handlers
const handleLinkClick = (link: NavigationLink, event: Event) => {
  event.preventDefault();
  console.log("Navigation link clicked:", link);
};

const handleBrandClick = (event: Event) => {
  event.preventDefault();
  console.log("Brand clicked");
};

const handleMobileToggle = (isOpen: boolean) => {
  console.log("Mobile menu toggled:", isOpen);
};
</script>

<template>
  <div class="app">
    <!-- Navbar Component -->
    <Navbar
      :navigation="defaultNavigation"
      :brand="defaultBrand"
      :sticky="true"
      @link-click="handleLinkClick"
      @brand-click="handleBrandClick"
      @mobile-toggle="handleMobileToggle"
    />

    <!-- Simple content to show the navbar works -->
    <main class="main-content">
      <div class="container">
        <h1>Navbar Component</h1>
        <p>
          This is a clean implementation of a responsive Vue 3 navbar component.
        </p>
        <p>
          Try resizing the window or using a mobile device to see the responsive
          behavior.
        </p>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: var(--color-background);
}

.main-content {
  padding: var(--space-8) 0;
}

.main-content h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.main-content p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
  line-height: var(--line-height-relaxed);
}
</style>
