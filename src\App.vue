<script setup lang="ts">
import { ref, onMounted } from "vue";
import Navbar from "./components/Navbar.vue";
import { useNavigation } from "./composables/useNavigation";
import {
  defaultNavigation,
  defaultBrand,
  simpleNavigation,
  navigationWithIcons,
  ecommerceNavigation,
  brandConfigs,
} from "./data/sampleNavigation";
import type { NavigationLink } from "./types/navigation";

// Navigation state
const currentNavigation = ref(defaultNavigation);
const currentBrand = ref(defaultBrand);
const currentPage = ref("Home");

// Navigation composable
const { setActiveLink, findLinkById } = useNavigation(currentNavigation.value);

// Demo configurations
const demoConfigs = [
  {
    name: "Default Navigation",
    navigation: defaultNavigation,
    brand: defaultBrand,
  },
  {
    name: "Simple Navigation",
    navigation: simpleNavigation,
    brand: brandConfigs.simple,
  },
  {
    name: "Navigation with Icons",
    navigation: navigationWithIcons,
    brand: brandConfigs.withLogo,
  },
  {
    name: "E-commerce Navigation",
    navigation: ecommerceNavigation,
    brand: brandConfigs.logoOnly,
  },
];

const currentConfigIndex = ref(0);

// Event handlers
const handleLinkClick = (link: NavigationLink, event: Event) => {
  event.preventDefault();
  console.log("Navigation link clicked:", link);

  // Update active state
  setActiveLink(link.id);

  // Update current page
  currentPage.value = link.label;

  // Simulate page navigation
  window.history.pushState({}, "", link.href);
};

const handleBrandClick = (event: Event) => {
  event.preventDefault();
  console.log("Brand clicked");
  currentPage.value = "Home";
  setActiveLink("home");
  window.history.pushState({}, "", "/");
};

const handleMobileToggle = (isOpen: boolean) => {
  console.log("Mobile menu toggled:", isOpen);
};

const switchDemo = (index: number) => {
  const config = demoConfigs[index];
  currentNavigation.value = config.navigation;
  currentBrand.value = config.brand;
  currentConfigIndex.value = index;

  // Reset to first link as active
  if (config.navigation.length > 0) {
    setActiveLink(config.navigation[0].id);
    currentPage.value = config.navigation[0].label;
  }
};

// Initialize
onMounted(() => {
  // Set initial active link
  const homeLink = findLinkById("home");
  if (homeLink) {
    setActiveLink("home");
  }
});
</script>

<template>
  <div class="app">
    <!-- Navbar Component -->
    <Navbar
      :navigation="currentNavigation"
      :brand="currentBrand"
      :sticky="true"
      @link-click="handleLinkClick"
      @brand-click="handleBrandClick"
      @mobile-toggle="handleMobileToggle"
    />

    <!-- Main Content -->
    <main class="main-content">
      <div class="container">
        <!-- Demo Controls -->
        <section class="demo-controls">
          <h2>Navbar Component Demo</h2>
          <p>
            Switch between different navigation configurations to see the
            component in action.
          </p>

          <div class="demo-buttons">
            <button
              v-for="(config, index) in demoConfigs"
              :key="index"
              :class="[
                'demo-button',
                { 'demo-button--active': currentConfigIndex === index },
              ]"
              @click="switchDemo(index)"
            >
              {{ config.name }}
            </button>
          </div>
        </section>

        <!-- Current Page Content -->
        <section class="page-content">
          <h1>{{ currentPage }}</h1>
          <p>
            This is a demo page showing the
            <strong>{{ currentPage }}</strong> section.
          </p>

          <div class="content-grid">
            <div class="content-card">
              <h3>🎯 Features</h3>
              <ul>
                <li>Mobile-first responsive design</li>
                <li>Dynamic navigation data loading</li>
                <li>Dropdown menu support</li>
                <li>Accessibility features (ARIA, keyboard navigation)</li>
                <li>Smooth animations and transitions</li>
                <li>TypeScript support</li>
                <li>Vue 3 Composition API</li>
              </ul>
            </div>

            <div class="content-card">
              <h3>📱 Mobile Features</h3>
              <ul>
                <li>Hamburger menu toggle</li>
                <li>Touch-friendly interactions</li>
                <li>Collapsible submenu support</li>
                <li>Overlay background</li>
                <li>Smooth slide animations</li>
                <li>Body scroll prevention</li>
              </ul>
            </div>

            <div class="content-card">
              <h3>♿ Accessibility</h3>
              <ul>
                <li>ARIA labels and roles</li>
                <li>Keyboard navigation support</li>
                <li>Screen reader compatibility</li>
                <li>Focus management</li>
                <li>High contrast mode support</li>
                <li>Reduced motion respect</li>
              </ul>
            </div>

            <div class="content-card">
              <h3>🎨 Customization</h3>
              <ul>
                <li>CSS custom properties</li>
                <li>Design system foundation</li>
                <li>Theme support (light/dark)</li>
                <li>Flexible brand configuration</li>
                <li>Icon support</li>
                <li>Custom styling options</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Usage Example -->
        <section class="usage-example">
          <h2>Usage Example</h2>
          <pre><code>&lt;Navbar
  :navigation="navigationLinks"
  :brand="{ name: 'Your Brand', href: '/' }"
  :sticky="true"
  @link-click="handleLinkClick"
  @brand-click="handleBrandClick"
  @mobile-toggle="handleMobileToggle"
/&gt;</code></pre>
        </section>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: var(--color-background);
}

.main-content {
  padding-top: var(--space-8);
  padding-bottom: var(--space-16);
}

/* Demo Controls */
.demo-controls {
  margin-bottom: var(--space-12);
  text-align: center;
}

.demo-controls h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.demo-controls p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-6);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.demo-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  justify-content: center;
}

.demo-button {
  padding: var(--space-3) var(--space-5);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background-color: var(--color-surface-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.demo-button:hover {
  color: var(--color-text-primary);
  background-color: var(--color-surface-tertiary);
  border-color: var(--color-border-secondary);
}

.demo-button--active {
  color: var(--color-text-inverse);
  background-color: var(--color-primary-600);
  border-color: var(--color-primary-600);
}

.demo-button--active:hover {
  background-color: var(--color-primary-700);
  border-color: var(--color-primary-700);
}

/* Page Content */
.page-content {
  margin-bottom: var(--space-12);
}

.page-content h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.page-content > p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-8);
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

@media (min-width: 768px) {
  .content-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .content-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-8);
  }
}

.content-card {
  padding: var(--space-6);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.content-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.content-card ul {
  margin: 0;
  padding-left: var(--space-5);
  list-style-type: disc;
}

.content-card li {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
  line-height: var(--line-height-relaxed);
}

.content-card li:last-child {
  margin-bottom: 0;
}

/* Usage Example */
.usage-example {
  background-color: var(--color-surface-secondary);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border-primary);
}

.usage-example h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.usage-example pre {
  background-color: var(--color-surface-tertiary);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  margin: 0;
}

.usage-example code {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
}

/* Mobile Responsive */
@media (max-width: 767px) {
  .demo-buttons {
    flex-direction: column;
    align-items: center;
  }

  .demo-button {
    width: 100%;
    max-width: 300px;
  }

  .content-card {
    padding: var(--space-4);
  }

  .usage-example {
    padding: var(--space-4);
  }

  .usage-example pre {
    padding: var(--space-3);
  }
}
</style>
