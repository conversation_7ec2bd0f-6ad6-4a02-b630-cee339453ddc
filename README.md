# Vue 3 Responsive Navbar Component

A comprehensive, accessible, and highly customizable navigation bar component built with Vue 3, TypeScript, and modern CSS. Features mobile-first responsive design, dropdown menus, keyboard navigation, and extensive accessibility support.

## ✨ Features

- **🎯 Vue 3 Composition API** - Built with modern Vue 3 and TypeScript
- **📱 Mobile-First Responsive** - Optimized for all screen sizes with hamburger menu
- **♿ Accessibility First** - ARIA labels, keyboard navigation, screen reader support
- **🎨 Highly Customizable** - CSS custom properties and flexible theming
- **🔄 Dynamic Navigation** - Reactive data loading and state management
- **📂 Dropdown Support** - Multi-level nested navigation menus
- **⚡ Smooth Animations** - Performant transitions with reduced motion support
- **🎪 DRY Architecture** - Reusable components and composables

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Basic Usage

```vue
<template>
  <Navbar
    :navigation="navigationLinks"
    :brand="brandConfig"
    :sticky="true"
    @link-click="handleLinkClick"
    @brand-click="handleBrandClick"
    @mobile-toggle="handleMobileToggle"
  />
</template>

<script setup lang="ts">
import Navbar from "./components/Navbar.vue";
import type { NavigationLink } from "./types/navigation";

const navigationLinks: NavigationLink[] = [
  {
    id: "home",
    label: "Home",
    href: "/",
    active: true,
  },
  {
    id: "about",
    label: "About",
    href: "/about",
  },
  {
    id: "services",
    label: "Services",
    href: "/services",
    children: [
      {
        id: "web-dev",
        label: "Web Development",
        href: "/services/web-development",
      },
      {
        id: "mobile-dev",
        label: "Mobile Development",
        href: "/services/mobile-development",
      },
    ],
  },
];

const brandConfig = {
  name: "Your Brand",
  href: "/",
};

const handleLinkClick = (link: NavigationLink, event: Event) => {
  console.log("Link clicked:", link);
  // Handle navigation
};

const handleBrandClick = (event: Event) => {
  console.log("Brand clicked");
  // Handle brand navigation
};

const handleMobileToggle = (isOpen: boolean) => {
  console.log("Mobile menu toggled:", isOpen);
};
</script>
```

## 📋 Component API

### Navbar Props

| Prop               | Type                                              | Default     | Description                     |
| ------------------ | ------------------------------------------------- | ----------- | ------------------------------- |
| `navigation`       | `NavigationLink[] \| NavigationSection[]`         | `[]`        | Navigation links or sections    |
| `brand`            | `BrandConfig`                                     | `undefined` | Brand/logo configuration        |
| `sticky`           | `boolean`                                         | `false`     | Whether navbar should be sticky |
| `className`        | `string`                                          | `''`        | Additional CSS classes          |
| `showMobileToggle` | `boolean`                                         | `true`      | Show mobile menu toggle         |
| `maxWidth`         | `'sm' \| 'md' \| 'lg' \| 'xl' \| '2xl' \| 'full'` | `'xl'`      | Maximum width                   |

### Navbar Events

| Event           | Payload                                | Description                               |
| --------------- | -------------------------------------- | ----------------------------------------- |
| `link-click`    | `(link: NavigationLink, event: Event)` | Emitted when a navigation link is clicked |
| `brand-click`   | `(event: Event)`                       | Emitted when brand/logo is clicked        |
| `mobile-toggle` | `(isOpen: boolean)`                    | Emitted when mobile menu is toggled       |

### TypeScript Interfaces

#### NavigationLink

```typescript
interface NavigationLink {
  id: string; // Unique identifier
  label: string; // Display text
  href: string; // URL or route path
  icon?: string; // Optional icon
  external?: boolean; // Opens in new tab
  active?: boolean; // Currently active
  disabled?: boolean; // Disabled state
  children?: NavigationLink[]; // Nested links
  className?: string; // Additional CSS classes
  attributes?: Record<string, string>; // Custom attributes
}
```

#### BrandConfig

```typescript
interface BrandConfig {
  name?: string; // Brand name
  logo?: string; // Logo URL
  href?: string; // Brand link URL
  alt?: string; // Logo alt text
}
```

## 🎨 Customization

### CSS Custom Properties

The component uses CSS custom properties for easy theming:

```css
:root {
  /* Colors */
  --navbar-background: #ffffff;
  --navbar-border: #e5e7eb;
  --navbar-text: #111827;
  --navbar-text-hover: #2563eb;
  --navbar-text-active: #1d4ed8;

  /* Spacing */
  --space-4: 1rem;
  --space-6: 1.5rem;

  /* Typography */
  --font-size-base: 1rem;
  --font-weight-medium: 500;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
}
```

### Dark Theme Support

The component automatically adapts to dark mode:

```css
@media (prefers-color-scheme: dark) {
  :root {
    --navbar-background: #111827;
    --navbar-text: #f3f4f6;
    --navbar-text-hover: #60a5fa;
  }
}
```

## 🔧 Advanced Usage

### Using Navigation Composable

```typescript
import { useNavigation } from "./composables/useNavigation";

const {
  setNavigation,
  setActiveLink,
  findLinkById,
  addNavigationItem,
  removeNavigationItem,
} = useNavigation(initialNavigation);

// Dynamically update navigation
setNavigation(newNavigationData);

// Set active link
setActiveLink("home");

// Add new navigation item
addNavigationItem({
  id: "new-page",
  label: "New Page",
  href: "/new-page",
});
```

### Accessibility Features

```typescript
import { useAccessibility } from "./composables/useAccessibility";

const { announceToScreenReader, trapFocus, restoreFocus } = useAccessibility({
  enableKeyboardNavigation: true,
  enableScreenReaderAnnouncements: true,
});

// Announce to screen readers
announceToScreenReader("Navigation menu opened");
```

### Animation Control

```typescript
import { useAnimations } from "./composables/useAnimations";

const { fadeIn, slideDown, animateDropdownOpen } = useAnimations({
  respectReducedMotion: true,
  defaultDuration: 250,
});

// Animate elements
await fadeIn(element);
await animateDropdownOpen(dropdown);
```

## 📱 Mobile Features

- **Hamburger Menu**: Animated toggle button
- **Touch Interactions**: Optimized for touch devices
- **Overlay Background**: Prevents interaction with content
- **Body Scroll Lock**: Prevents scrolling when menu is open
- **Collapsible Submenus**: Expandable nested navigation

## ♿ Accessibility Features

- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard support
- **Focus Management**: Intelligent focus handling
- **Screen Reader Support**: Announcements and live regions
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user motion preferences

## 🎯 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📁 Project Structure

```
src/
├── components/
│   ├── Navbar.vue              # Main navbar component
│   ├── NavbarLink.vue          # Individual navigation link
│   ├── NavbarDropdown.vue      # Dropdown menu component
│   └── NavbarMobileLink.vue    # Mobile navigation link
├── composables/
│   ├── useNavigation.ts        # Navigation state management
│   ├── useAccessibility.ts     # Accessibility utilities
│   └── useAnimations.ts        # Animation utilities
├── types/
│   └── navigation.ts           # TypeScript interfaces
├── styles/
│   └── navbar.css              # Component styles
├── data/
│   └── sampleNavigation.ts     # Sample navigation data
└── style.css                   # Global styles and design system
```

## 🧪 Testing

Run the development server to test the component:

```bash
npm run dev
```

The demo page includes multiple navigation configurations to test different scenarios:

- Simple navigation
- Navigation with dropdowns
- Navigation with icons
- E-commerce style navigation

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For questions or issues, please open an issue on GitHub or contact the development team.
